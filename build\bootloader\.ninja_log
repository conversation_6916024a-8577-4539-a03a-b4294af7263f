# ninja log v5
2	228	7766730878925150	project_elf_src_esp32c3.c	805371ff9dbd6278
23	318	7766730880352401	esp-idf/hal/CMakeFiles/__idf_hal.dir/cpu_hal.c.obj	63c0dd055ccd4d95
43	668	7766730881332862	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	34163223056e59b7
14	678	7766730881953623	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	7cf7a5adb322fb7b
32	694	7766730882143614	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	74da7bfb79855796
107	709	7766730883197039	esp-idf/soc/CMakeFiles/__idf_soc.dir/soc_include_legacy_warn.c.obj	93b0227454eac0d5
195	728	7766730883397040	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	c4bbc1697e2f221d
228	740	7766730883207038	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	b5bb879f74d9a456
210	756	7766730884107043	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sigmadelta_periph.c.obj	6b55f09c90943268
179	767	7766730884207048	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	5a25fdbad0fcab9c
159	791	7766730884197037	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	166b112b4f572b79
186	1076	7766730884257048	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	e46f3b36376c4f11
709	1085	7766730888076991	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	f77bca543ff59695
742	1094	7766730887507010	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp32c3/spi_flash_rom_patch.c.obj	26f89adb96c157a1
131	1111	7766730885190247	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	e36d77f5ab63b78
168	1140	7766730885230248	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	d7fe2646a2f88056
202	1149	7766730885440246	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	dd88855e52cfcf4
668	1184	7766730886862164	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	bd53e93264186459
585	1199	7766730887242167	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	421dc701f881fb4c
678	1215	7766730887797006	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	692056e993ba2c6
694	1234	7766730888067012	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	8a436250bef625a7
757	1254	7766730889218670	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	7b5b7fc1fe0bc335
767	1495	7766730892148400	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	b516506b727b666b
729	1576	7766730892996134	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	b9e406a4a016cab9
1085	1671	7766730893186142	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	9e6b2ef9bfd369bb
1140	1688	7766730893471769	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	2a847b90c79e838d
1216	1705	7766730893831767	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	c084e1055ec66bc9
1095	1752	7766730894710711	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	90b216f410cbd5c4
1190	1771	7766730894840713	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	4fedf555ad8ad71d
1068	1834	7766730895355704	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e2b94b1948999927
1234	1859	7766730895765701	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	6391392906e5b25f
1077	1888	7766730896055700	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash_config_esp32c3.c.obj	8d8a32d467806050
1169	2021	7766730897350473	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	e48fa079c817769c
1200	2029	7766730897528473	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash.c.obj	fa411cd54f315870
1689	2073	7766730897768475	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	51ad5fcbe9a05032
1495	2108	7766730897888473	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	cef01c0d30eef03e
1671	2126	7766730898519868	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse_esp32c3.c.obj	7c18fea2ffc15de6
1638	2180	7766730898869862	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	1ddd40b3f3fa46ad
1255	2307	7766730900137538	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	52fed6b551a8b17f
1112	2365	7766730900747082	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_qio_mode.c.obj	61cf0cba06290473
1753	2376	7766730900872099	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	d333b894287796d5
1818	2422	7766730901467403	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	3d43afd9417f96d6
1705	2430	7766730901504152	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	c98218917c638d93
1835	2440	7766730901548772	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	5c5223d3dfee84bc
1938	2605	7766730903306396	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	a4ada9b399d8e81a
2109	2656	7766730903579875	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	8d18271409505bfc
1860	2676	7766730903771245	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	645a00b420818a71
2377	2836	7766730905573107	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu_util.c.obj	c06720461089e988
2441	2855	7766730905786209	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/compare_set.c.obj	6f23aeac647181eb
2307	2868	7766730905685308	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	419cf0c25cc75040
2366	2876	7766730905857737	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	85a9fd41f10729e3
2022	2885	7766730905961088	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8d1a530a90ca555e
2422	2896	7766730905971087	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_util_esp32c3.c.obj	c6d9cf5da13cf91c
2059	2913	7766730906236105	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	5a7beb6e619f6475
2184	2934	7766730906394347	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api_key_esp32xx.c.obj	7c9c9cf0c71ca47b
2430	2947	7766730906544386	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	85d38a4b1f92f583
2605	3009	7766730907328639	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	80390d3fcaebeb3d
2093	3021	7766730907369159	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	221aa2a339dfe4bc
2678	3363	7766730910871048	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	e1f37df8944d0198
2855	3388	7766730911171215	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	5f04f006dce217cb
2934	3395	7766730911241210	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e00798d029204319
2126	3396	7766730911252847	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	c31bff827da41608
2913	3414	7766730911437946	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	79d10eec2253b615
2837	3433	7766730911607995	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_tjpgd.c.obj	8d90b0f3bebee493
3010	3439	7766730911648013	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	22df1226a8c9f4a0
2877	3440	7766730911668011	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	7f65037c91cc88b9
3021	3442	7766730911668011	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	22e4b3062cf0afcc
2947	3442	7766730911638013	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	1eb9deefe0156623
2657	3443	7766730911698012	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	d3c5ca828d943e98
2897	3457	7766730911868503	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_pm.c.obj	eff81f35606b997f
2886	3458	7766730911878011	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	18a77439f5aa066e
2870	3461	7766730911908017	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	95c00c50a9a65404
3363	3514	7766730912435377	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	34aab3c5572b5391
3414	3941	7766730916722974	esp-idf/log/liblog.a	45f8bb6c82ccaecc
3944	4016	7766730917472981	esp-idf/esp_rom/libesp_rom.a	807a0753e0bcecd8
4019	4093	7766730918251665	esp-idf/esp_common/libesp_common.a	ad186c6e65dafc0b
4096	4180	7766730919111666	esp-idf/esp_hw_support/libesp_hw_support.a	fde01190b7ed7b3c
4180	4249	7766730919804022	esp-idf/esp_system/libesp_system.a	3f8c556360706535
4249	4327	7766730920577710	esp-idf/efuse/libefuse.a	df4fe4dd49ddbf51
4329	4423	7766730921538897	esp-idf/bootloader_support/libbootloader_support.a	73dd953693143db7
4423	4494	7766730922248757	esp-idf/spi_flash/libspi_flash.a	eb28f45045147fce
4494	4568	7766730922984217	esp-idf/micro-ecc/libmicro-ecc.a	60b135e0860bd893
4568	4663	7766730923936364	esp-idf/soc/libsoc.a	d3fa1bd5012d5159
4663	4737	7766730924678611	esp-idf/hal/libhal.a	db72c761c1dc6791
4737	4807	7766730925386761	esp-idf/main/libmain.a	eb66395fa1c5737d
4808	4980	7766730927077418	bootloader.elf	6e02a2e5199973ff
4980	5323	7766730930546518	.bin_timestamp	1689441c7caefa08
5323	5407	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	6439810ec987903f
4	62	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	6439810ec987903f
