#ifndef       				 _USER_COMMON_H_
#define       				 _USER_COMMON_H_


/****************************************error code****************************************/
#define	RPT_SUCCESS	0x01	//执行成功
#define	RPT_RECLEN	0x02	//包头解析错误
#define	RPT_LENGTH	0x03	//接收数据的包长度数据错误
#define	RPT_PACKLEN	0x04	//接收数据包长度大于缓冲区长度
#define	RPT_PACKAGE	0x05	//接收数据的数据包解析出错
#define	RPT_STATE	0x06	//状态处理出错了
#define	RPT_NOSUP	0x07	//不支持的接口
#define	RPT_BLOCK	0x08	//阻塞了
#define	RPT_TAGLEN	0x09	//读取的标签长度不对
#define	RPT_FOCUSING	0x0a	//正在集中扫描，请求被拒绝
#define	RPT_SCANING	0x0b	//有扫描任务正在执行中，日常扫描被拒绝
#define	RPT_RXSMALL	0x0c	//接收缓冲区过小
#define	RPT_FAILED	0xFF	//执行失败，不知名原因
#define	RPT_TXSMALL	0x0d	//发送缓冲区过小
#define	RPT_TXERR	0x0e	//发送数据长度出错
#define	RPT_WFLASH	0x0f	//写flash出错
#define	RPT_HEADER	0x10	//接收数据包头长度不够
#define	RPT_OTHER	0x11	//接收到不支持的指令
#define	RPT_NTTERR	0x12	//非透传状态下收到透传指令
#define	RPT_NOTAG	0x13	//没有标签
#define RPT_STOP	0x14	//停止集中扫描

/******************************************source*******************************************/
#define			    		 IS_SOCKET						 0x01
#define			    		 IS_RFID						 0x02
#define			    		 IS_SERIAL						 0x04
#define			    		 IS_TSS  						 0x05


/*****************************************led和蜂鸣器****************************************/
unsigned char		         PART_STATE_OFF = 0x00;						    //关
unsigned char		         PART_STATE_ON  = 0x01;						    //开

unsigned char		         g_beep_count = 0;						    //设备电压
unsigned char		         g_whitle_count = 0;						 //设备电压
unsigned char		         g_yellow_count = 0;						 //设备电压
unsigned char                g_whiteled_state = 0x01;
unsigned char                g_beep_state = 0x01;


/*******************************************flash部分****************************************/
const char                   g_https_url[] = "http://***************:8089/wifi_TcpClient.bin";
const char                   g_server_cert_pem_start[] = "8888888***********";
const char      			 g_namespace[] = "name_space";
const char      			 g_suartTaskName[] = "_uart_rx_task";
const char      			 g_sconrxTaskName[] ="tcp_client_connect";
const char      			 g_snetrwTaskName[] = "_net_rw_task";
const char      			 g_srefTaskName[] = "_net_focus_task";
const char      			 g_sota_http_task[] = "ota_http_task";
const char                   g_daily_timer_name[] = "daily_timer";
const char                   g_focus_timer_name[] = "focus_timer";

const char					 g_shardware_version[] = "hardware_version";
const char					 g_sfirmware_version[] = "firmware_version";
const char					 g_sprotocol_version[] = "protocol_version";
const char					 g_sreader_ver[] = "reader_ver";
const char					 g_sdevice_id[] = "device_id";
const char	    			 g_cswifi_ssid[] = "swifi_ssid";
const char      			 g_cswifi_password[] = "swifi_password";
const char      			 g_cserver_ip[] = "server_ip";
const char      			 g_cserver_port[] = "server_port";
const char      			 g_sbeep_state[] = "beep_state";
const char      			 g_srepeat_filter_time[] = "repeat_filter_time";
const char					 g_stransparent_transmission[] = "transparent_transmission";
const char      			 g_sheart_state[] = "heart_state";
const char					 g_sheart_interval[] = "heart_interval";
const char					 g_sdaily_scan_state[] = "heart_interval";
const char					 g_sdaily_interval[] = "daily_interval";
const char					 g_sdaily_time[] = "daily_time";
const char					 g_sfocus_time[] = "focus_time";
const char					 g_sfocus_interval[] = "focus_interval";
const char					 g_sdown_count[] = "rfid_count";
const char					 g_sdown_tag[] = "sheet_data";

const char					 g_sdaily_way[] = "daily_way";
const char					 g_sfocus_way[] = "focus_way";
const char					 g_sup_heart[] = "up_heart";
const char					 g_sframe_header[] = "frame_header";
const char					 g_svoltage[] = "voltage";
const char					 g_seconomizen[] = "economizen";
const char					 g_sdevice_min_power[] = "device_min_power";
const char					 g_sdevice_max_power[] = "device_max_power";
const char					 g_santenna_count[] = "antenna_count";
const char					 g_santenna_power[] = "antenna_power";
const char      			 g_sthreshold[] = "threshold";
const char					 g_sidle_time[] = "sidle_time";
const char      			 g_sscan_time[] = "scan_time";
const char					 g_sonline_state[] = "online_state";
const char      			 g_supdate_state[] = "update_state";
const char					 g_sis_netrec[] = "is_netrec";
const char      			 g_sis_netsend[] = "is_netsend";
const char					 g_sscan_type[] = "scan_type";
const char					 g_sdaily_state[] = "daily_state";
const char					 g_sfocus_state[] = "focus_state";



/******************************************网络******************************************/
static const char			 g_sServerUpdate[] = "https://***************:8089/wifi_TcpClient.bin";//升级用文件地址
static const char			 g_sdefault_ssid[] = "BY-WIFI";
static const char			 g_sdefault_password[] = "by88888888";
static const char			 g_sdefault_ip[] = "*************";//"**************";//
static const unsigned short  g_ndefault_port = 8160;//  
static const char            g_sdefault_device_id[] = "ZZZZ";

struct       		 		 sockaddr_in             g_server_addr;                  //server地址
int                 		 g_connect_socket        = 0;                           //连接socket
bool                		 g_rxtx_need_restart     = false;                       //异常后，重新连接标记


/*******************************************串口*******************************************/
#define			    		 BAUD_RATE						 115200
#define			    		 RXD1_PIN 			             GPIO_NUM_18
#define			    		 TXD1_PIN 			             GPIO_NUM_19
#define						 RX1_BUF_SIZE 					 1446  //(MAX_COUNT*TAG_LEN+PACKHEADER_LEN+1)
#define						 TX1_BUF_SIZE 					 2000

/***************************************读卡器部分***************************************/
//第0个字节高位
#define						 FRAME_HEADER		       0x5A	  //帧头,1个字节
/*共4个字节
0011200 : 协议控制字（0001-->31-24 与 23-16 位组成，协议类型号 0+协议版本号 1，占 2 字节；1-->15-14 位为 00，
            13 位 Rs485 标志为 0，12 位主动上传消息标志位为 1，组成 0001，最终为 1；
            2-->11-8 位表示 RFID 设置与操作消息类别，类别为 0002，
            最终为 2；00-->7-0 位，进入读卡状态，此时读写器主动上传消息标志位应置为 1，上传内容 MID 为 00，最终是 00)*/
//第1个字节高位
#define             		 FIRST_DEF                 0x00    //协议控制字
//第2个字节高位
#define             		 SECOND_DEF                0x01    //协议控制字
//第3个字节低位
#define             		 THIRD_CONFIG              0x02    //协议控制字

//第4个字节低位
#define             		 RECEIVE_QUERY_READER      0x00    //查询读写器 RFID 能力
#define             		 RECEIPT_SET_POWER         0x01    //设置功率
#define             		 RECEIVE_GET_POWER         0x02    //查询功率
#define             		 RECEIPT_SET_UPPARAMS      0x09    //设置标签上传参数
#define             		 RECEIPT_QUERY_TAG_4       0x10    //查询标签
#define             		 RECEIVE_GET_UPPARAMS      0x0A    //查询标签上传参数，第4个字节高位，共4个字节
#define             		 RECEIVE_GET_IDLE          0x0E    //查询读写器自动空闲模式，第4个字节高位，共4个字节
#define             		 RECEIPT_STOP_CMD          0xFF    //停止命令

//第3个字节低位
#define             		 THIRD_RECEIVE             0x12    //协议控制字
//第4个字节低位
#define             		 RECEIVE_TAG_DATA          0x00    //扫描的标签数据
#define             		 RECEIVE_END_UPTAG         0x01    //结束上传标签

//第11个字节(从高位算)
#define             		 QUERY_TAG_ONE             0x00    //RECEIVE_TAG_DATA的后面，扫描的标签数据
#define             		 QUERY_TAG_CONTINUE        0x01    //RECEIVE_TAG_DATA的后面，扫描的标签数据

//第3个字节低位
#define             		 THIRD_CONFIRM              0x11    //协议控制字
//设置标签上传参数    5A00010209000501000A0200B8D8
// 5A  : 帧头
// 00010209  : 协议控制字
// 0005  : 长度
// 01  : 重复过滤时间 PID
// 07D0  : 重复过滤时间 (设置默认重复过滤时=2000ms,日常扫描时长=20(S)[20*1000/10ms]****数据需要动态修改
// 02  : RSSI 阈值 PID
// 00  : RSSI 阈值 0
// B8D8  : crc 校验码
const unsigned char g_reader_set_upparam[14] = {0x5A,0x00,0x01,0x02,0x09,0x00,0x05,0x01,0x01,0xF4,0x00,0x00,0xB8,0xD8};

//查询标签上传参数   5A0001020A000080F8
// 5A  :  帧头
// 0001020A  :  协议控制字
// 0000  :  长度
// 80F8  : crc 校验码
const unsigned char g_reader_get_upparam[9] = {0x5A,0x00,0x01,0x02,0x0A,0x00,0x00,0x80,0xF8};

//设置功率:          5A000102010008011E021E031E041E4C28
// 5A  : 帧头
// 00010201  : 协议控制字
// 0008  : 长度
// 01  : 天线端口 1 功率 PID
// 1E  : 天线 1 端口功率 30
// 02  : 天线端口 2 功率 PID
// 1E  : 天线 2 端口功率 30
// 03  : 天线端口 3 功率 PID
// 1E  : 天线 3 端口功率 30
// 04  : 天线端口 4 功率 PID
// 1E  : 天线 4 端口功率 30
// 4C28  :  crc 校验码
const unsigned char g_reader_set_power[17] = { 0x5A,0x00,0x01,0x02,0x01,0x00,0x08,0x01,0x1E,0x02,0x1E,0x03,0x1E,0x04,0x1E,0x4C,0x28 };

//查询功率 :  5A0001020200002959
// 5A  : 帧头
// 00010202  : 协议控制字
// 0000  : 长度
// 2959  : crc 校验码
const unsigned char g_reader_get_power[9] = { 0x5A,0x00,0x01,0x02,0x02,0x00,0x00,0x29,0x59 };

//设置读写器自动空闲模式 5A0001020D00040101000AE747
// 5A : 帧头
// 0001020D : 协议控制字
// 0004 : 长度
// 01 : 间隔模式使能 使能
// 01 : 空闲时间 PID
// 09C4 : 空闲时间 (设置默认时间=2500ms,[日常扫描间隔时间(40s)-日常扫描时间(15s)=25*1000/10ms]****数据需要动态修改
// 05DC : 读卡时间 (设置默认时间=1500ms,日常扫描时间15(S)[15*1000/10ms]****数据需要动态修改
// E747 : crc 校验码                      
const unsigned char g_reader_set_idle[15] = { 0x5A,0x00,0x01,0x02,0x0D,0x00,0x06,0x01,0x01,0x01,0xF4,0x01,0xF4,0xE7,0x47 };


//接收数据-查询读写器自动空闲模式 : 5A0001020E00005C38
// 5A  :  帧头
// 0001020E  :  协议控制字
// 0000  :  长度
// 5C38  :  crc 校验码
const unsigned char g_reader_get_idle[9] = { 0x5A,0x00,0x01,0x02,0x0E,0x00,0x00,0x5C,0x38 };

//单次查询  : Send  :    5A00010210000800000001000200069BBC 
// 5A  : 帧头
// 00010210  : 协议控制字，00010210不自动上报--->自动上报00011210    
// 0008  : 数据长度
// 00000001  : 天线端口，1 号天线
// 01  : 连续读取
// 02  : TID 读取参数 PID
// 00  : TID 读取模式
// 06  : TID 读取长度
// ED08  : crc 校验码
const unsigned char g_reader_set_one[17]    =  { 0x5A,0x00,0x01,0x02,0x10,0x00,0x08,0x00,0x00,0x00,0x01,0x00,0x02,0x00,0x06,0x9B,0xBC };
//循环查询  :    5A0001021000080000000101020006ED08
// 5A  : 帧头
// 00010210  : 协议控制字
// 0008  : 数据长度
// 00000001  : 天线端口，1 号天线
// 01  : 连续读取
// 02  : TID 读取参数 PID
// 00  : TID 读取模式
// 06  : TID 读取长度
// ED08  : crc 校验码
const unsigned char g_reader_set_coninue[17] = { 0x5A,0x00,0x01,0x02,0x10,0x00,0x08,0x00,0x00,0x00,0x01,0x01,0x02,0x00,0x06,0xED,0x08 };

//停止命令:         5A000102FF0000885A
// 5A  : 帧头
// 000102FF  : 协议控制字
// 0000  : 长度
// 885A  : crc 校验码
const unsigned char g_reader_set_stop[9] = { 0x5A,0x00,0x01,0x02,0xFF,0x00,0x00,0x88,0x5A };


/************************************基站的定义命令***********************************/
//第0个字节
#define				STATION_FRAME_HEADER		0xFE		//自定义帧头
//第1个字节
#define				STATION_MY_CMD			    0xEF		//帧头低位

//第2个字节
#define				UP_STATION_HEART			0xb0		//[上行]心跳
#define				SET_STATION_RESET			0xb1		//[下行]复位操作/手动模式
#define				UP_STATION_ADDTAG	        0xb2		//[上行]上报日常扫描到多的标签
#define				UP_STATION_REDUCETAG 	    0xb3		//[上行]上报日常没有扫到的标签
#define				GET_STATION_PARAM   	    0xb4		//[下行]查询基站的工作参数
#define				SET_STATION_SAVE_TAG    	0xb5		//[下行]设置基站管理的标签数据
#define				GET_STATION_SAVE_TAG		0xb6		//[下行]查询基站管理的标签数据
#define				SET_STATION_BEGIN_FOCUS		0xb7		//[下行]设置集中扫描开始
#define				SET_STATION_DAILYPARAMS		0xb8		//[下行]设置日常扫描参数
#define				SET_STATION_HEARTPARAMS	    0xb9		//[下行]设置心跳参数
#define				SET_STATION_UPDATEFIRMWARE	0xba		//[下行]设置更新基站固件--暂时不支持
#define				SET_STATION_ECONOMIZEN		0xbb		//[下行]设置节能模块--暂时不支持
#define				SET_STATION_BEEPON			0xbc		//[下行]设置蜂鸣器响声--暂时不支持
#define				SET_STATION_FOCUSPARAMS		0xbd		//[下行]设置集中扫描参数
#define				SET_STATION_IDLESTATE  		0xbe		//[下行]设置读卡器的自动空闲状态
#define				GET_STATION_HEARTPARAMS	    0xbf		//[下行]查询心跳参数
#define				GET_STATION_DAILYPARAMS		0xc0		//[下行]查询日常扫描参数
#define				GET_STATION_FOCUSPARAMS		0xc1		//[下行]查询集中扫描参数
#define				SET_STATION_IDLESCAN		0xc2		//[下行]设置读卡器的空闲时间/扫描时间
#define				GET_STATION_SOFT_VER		0xc3		//[下行]查询基站版本
#define             SET_STATION_TT_STATE        0xc4        //[下行]设置基站的透传开关
#define             GET_STATION_TT_STATE        0xc5        //[下行]查询基站的透传开关
#define				SET_STATION_EARSE_PARAMS	0xc6		//[下行]设置基站擦除数据
#define             UP_STATION_ERROR            0Xc7        //[上行]上报基站发现的故障代码
#define			    SET_STATION_NETPARAMS		0xc8		//[下行]设置WIFI和服务器的参数
#define             GET_STATION_NETPARAMS       0xc9        //[下行]查询WIFI和服务器的参数
#define				SET_STATION_DEVICEID		0xca		//[下行]设置设备ID
#define				GET_STATION_DEVICEID		0xcb		//[下行]查询设备ID
#define             UP_STATION_NORMALTAG        0xcc        //[上行]上报日常扫描的正常的标签
#define				UP_STATION_ADD_REDUCE		0xcd		//[下行]上报日常扫描的增加和减少的标签
#define             UP_STATION_DAILY_ALL        0xce        //[上行]上报日常扫描的所有标签
#define             UP_STATION_RESTORE_SETTING  0xff        //[下行]设置为出厂默认设置

/************************************基站的保存数据***********************************/
#define				DEFAULT_HARDWARE_VER		       	0x03		//协议版本
#define				DEFAULT_FIRMWARE_VER		       	0x01		//协议版本
#define			    DEFAULT_PROTOCOL_VER			    0x36		//协议版本
#define				ANTENNA_COUNT			            0x04		//天线个数

unsigned char       g_hardware_version = DEFAULT_HARDWARE_VER;      //硬件版本
unsigned char       g_firmware_version = DEFAULT_FIRMWARE_VER;      //固件版本
unsigned char		g_protocol_version  = DEFAULT_PROTOCOL_VER;	    //[flash]协议版本
unsigned char       g_reader_ver;                                   //读卡器协议

char		        g_device_id[DEVID_LEN];	                //[flash]设备ID
char                g_swifi_ssid[SSID_LEN];			        //[flash]WIFI账号
char                g_swifi_password[PSWD_LEN];		        //[flash]WIFI密码
char                g_sserver_ip[IPADDRESS_LEN];		    //[flash]服务器IP
unsigned short      g_server_port;					        //[flash]服务器端口
unsigned char       g_beep_state;                           //[flash]蜂鸣器开关
unsigned char		g_voltage;						        //设备电压
unsigned char       g_economizen;                           //节能开关
unsigned char       g_device_min_power;                     //工作频段
unsigned char       g_device_max_power;                     //工作频段
unsigned char		g_antenna_count;				        //单个设备连接天线个数
unsigned char		g_antenna_power[ANTENNA_COUNT];         //单个设备的工作频率
unsigned short		g_repeat_filter_time;                   //[flash]重复过滤时长
unsigned char		g_threshold;                            //信号阈值    
unsigned char		g_idle_state;                           //自动空闲是否打开
unsigned short		g_idle_time;                            //自动空闲模式下的空闲时间
unsigned short		g_scan_time;                            //自动空闲模式下的扫描时间

unsigned char		g_online_state;							//基站在线状态
unsigned char		g_update_state;                         //固件更新数据
unsigned char		g_transparent_transmission;      	    //[flash]透传状态
unsigned char		g_is_netrec;							//是否正在接收数据
unsigned char		g_is_netsend;							//是否正在发送数据
unsigned char		g_heart_state;							//[flash]是否开启心跳
unsigned short		g_heart_interval;					    //[flash]设备心跳间隔
unsigned char		g_heart_is_send;  						//是否正在发送心跳数据
unsigned short		g_heart_intervaling;					//已经发生的延迟间隔
unsigned char       g_heartdata[MAX_HEART_LEN];             //心跳数据包

unsigned char		g_scan_type;							//当前执行的扫描类型
unsigned char		g_daily_scan_state;					    //[flash]设备是否开启日常扫描状态
unsigned char		g_daily_scan_way;						//[flash]设备日常扫描上报数据方式
unsigned short		g_daily_interval;					    //[flash]设备日常扫描间隔
unsigned short		g_daily_scantime;						//[flash]设备日常扫描时间
unsigned short		g_daily_intervaling;					//已经发生的时间
unsigned char		g_daily_is_working;						//是否正在执行
unsigned char		g_daily_scaning_state;					//是否正在扫描标签
unsigned char		g_daily_rec_state;						//日常扫描接收读卡器标签状态
unsigned char		g_daily_count;                          //日常扫描到的标签数量
unsigned short      g_daily_index;                          //当前扫描到的标签数据索引
unsigned char		g_daily_tag[RX1_BUF_SIZE];              //日常扫描的RFID标签
unsigned short		g_focus_interval;					    //[flash]设备集中扫描间隔
unsigned short		g_focus_scantime;						//[flash]设备集中扫描时间
unsigned short		g_focus_intervaling;					//已经发生的时间
unsigned char		g_focus_is_working;						//是否正在执行
unsigned char		g_focus_scaning_state;					//是否正在扫描标签
unsigned char		g_focus_rec_state;						//集中扫描接收读卡器标签状态
unsigned char		g_focus_count;                          //集中扫描到的标签数量
unsigned short      g_focus_index;                          //当前扫描到的标签数据索引
unsigned char		g_focus_tag[RX1_BUF_SIZE];              //日常扫描的RFID标签

unsigned char		g_down_rec_state;;						//标签数据是否接收成功
unsigned char		g_down_save_state;;						//标签数据是否保存成功
unsigned char		g_down_count;						    //[flash]下载的RFID标签个数
unsigned short 		g_down_index;						    //处理下载标签的当前数据索引
unsigned char	    g_down_tag[RX1_BUF_SIZE];	            //[flash]下载的RFID标签



/******************************************************主题函数声明**************************************************/
//定时器句柄
esp_timer_handle_t  g_daily_timer_handle = 0;
esp_timer_handle_t  g_focus_timer_handle = 0; 

//主函数
void app_main();


//心跳和日常扫描的定时器
bool _create_daily_timer();
//集中扫描的定时器
//bool _create_focus_timer();
//定时器回调函数任务
void _daily_timer_handle(void *arg);
//定时器回调函数任务
//void _focus_timer_handle(void *arg);


//主要处理逻辑,同时控制LED灯灯闪烁
void _main_task();


/***************************************flash部分***************************************/
/********读数据*******/
//从flash读取初始数据
bool _read_flash_alldata( );

/********写操作*******/
//将缓存数据全部写入flash
char _write_flash_alldata( );

//将透传状态写入flash
bool _write_flash_tt_state();
//将设备ID写入flash
char _write_flash_deviceid();
//将网络参数写入flash
char _write_flash_netparams();

/***************************************串口部分****************************************/
/*******初始化工作******/
//初始化UART
esp_err_t _uart_init( );


/******************串口读写*****************/
//串口测试函数
bool _uart_test();
//读串口和标签数据线程任务
void _uart_rx_task();
//解析接收串行数据
bool _receive_comdata(unsigned char* recdata, unsigned short index, unsigned short nlen);
//处理串口的命令
bool _deal_serialcmd( unsigned char* recdata, unsigned short index, unsigned short nlen,char source);


/******************读卡器部分***************/
/*******初始化工作******/
//初始化读卡器的工作参数
bool _init_reader_params();
//处理读卡器的命令
bool _deal_rfidcmd( unsigned char* recdata, unsigned short index, unsigned short nlen);


/*******设置读卡器参数******/
//[set]设置读卡器的自动空闲模式
void _set_reader_idle(unsigned short idletime,unsigned short scantime,short time);
//[set]设置读卡器的功率
void _set_reader_power(short time);
//[set]设置读卡器上传参数
void _set_reader_upparam(short time,unsigned short filter_time,unsigned char thresholdtime);
//[set]设置查询读卡器停止读，使读写器进入到空闲状态
void _set_reader_stop(short time);
//[set]设置读卡器开始上报单个标签
void _set_reader_one(short time);
//[set]设置读卡器开始上报多个标签
void _set_reader_coninue(short time);
	
	
/*******读取读卡器参数******/
//[get]查询读卡器的功率
bool _get_reader_power(short time);
//[get]查询读卡器的上传参数
bool _get_reader_upparam(short time);
//[get]查询读卡器的空闲模式参数
bool _get_reader_idle(short time);
	
	
	

// /*******从读卡器接收回执******/
//[RECEIPT] 查询标签
bool _receipt_reader_query_tag( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[RECEIPT] 停止命令
bool _receipt_reader_stop_cmd( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[RECEIPT] 设置功率
bool _receipt_reader_set_power( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[RECEIPT] 设置标签上传参数
bool _receipt_reader_uploadmode( unsigned char* recdata, unsigned short index, unsigned short nlen);

/*******从读卡器接收数据******/
//[receive] 接收标签数据
bool _receive_reader_tag( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive] 接收日常扫描标签数据
bool _receive_reader_daily_tag( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive] 接收集中扫描标签数据
bool _receive_reader_focus_tag( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive]查询读写器RFID能力
bool _receive_reader_querycable( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive]接收查询功率
bool _receive_reader_query_power( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive]查询标签上传参数
bool _receive_reader_query_params( unsigned char* recdata, unsigned short index, unsigned short nlen);
//[receive]查询读写器自动空闲模式
bool _receive_reader_query_idlemode( unsigned char* recdata, unsigned short index, unsigned short nlen);
//对收到的扫描标签去重
bool _compare_daily_duplitag(unsigned char* rec);
bool _compare_focus_duplitag(unsigned char* rec);


unsigned short CRC16_XMODEM (unsigned char *ptr, int len);

/***************************************网络部分****************************************/
/*******初始化工作******/
//初始化基站运行初数据
void _init_run_data();

//初始化基站保存数据
void _init_station_data();


/*****************socket部分****************/
//解析接收网络数据
bool _receive_netdata( unsigned char* recdata, unsigned short index, unsigned short nlen );
//处理上位机的命令
bool _deal_netcmd( unsigned char* recdata, unsigned short index, unsigned short nlen,char source);

/******************接口部分*****************/
//[下行]复位操作/手动模式
bool _set_station_reset( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询基站的工作参数
bool _get_station_params( unsigned char* recdata, unsigned short index, unsigned short nlen,char source );
//[下行]下载基站管理的标签数据
bool _set_station_save_tag( unsigned char* recdata, unsigned short index, unsigned short nlen,char source );
//[下行]查询基站管理的标签数据
bool _get_station_save_tag( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置集中扫描开始
bool _set_station_begin_focus( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置日常扫描参数
bool _set_station_dailyparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置心跳参数
bool _set_station_heartparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置更新基站固件--暂时不支持
bool _set_station_update_firmware(unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置节能模块--暂时不支持
bool _set_station_economizen( unsigned char* recdata, unsigned short index, unsigned short nlen,char source );
//[下行]设置蜂鸣器响声--暂时不支持
bool _set_station_beepon( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置集中扫描参数
bool _set_station_focusparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置读卡器的自动空闲状态
bool _set_station_idlestate( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询心跳参数
bool _get_station_heartparams( unsigned char* recdata, unsigned short index, unsigned short nlen,char source );
//[下行]查询日常扫描参数
bool _get_station_dailyparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询集中扫描参数
bool _get_station_focusparams( unsigned char* recdata, unsigned short index, unsigned short nlen,char source );
//[下行]设置读卡器的空闲时间/扫描时间
bool _set_station_idlescan( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询基站版本
bool _get_station_soft_ver( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置基站的透传开关
bool _set_station_tt_state( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询基站的透传开关
bool _get_station_tt_state( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置基站擦除数据
bool _set_station_earse_params(unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置WIFI和服务器的参数
bool _set_station_netparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询WIFI和服务器的参数
bool _get_station_netparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置设备ID
bool _set_station_deviceid( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]查询设备ID
bool _get_station_deviceid( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);
//[下行]设置为出厂默认设置
bool _up_station_restore_setting( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source);

//[上行]心跳
bool _up_station_heart( unsigned char source );
//[上行]上报基站发现的故障 
bool _up_station_errcode( unsigned char cmd,unsigned char code,char source);
//[上行]发送包含代码的执行结果回执 
bool _up_station_receipt(unsigned char cmd,unsigned char code,char source);
//向终端发送数据
bool _reply_data(unsigned char* txbuf,unsigned int ntx,char cmd,char source);
//[测试]上报基站发现的故障 
void _net_testout(  char *perr,char cmd, char code,char source);

/*******扫描处理部分******/
//对收到的扫描标签去重
void _tag_dupli_removal(unsigned char scan_type);
//主线程中处理集中扫描部分
void _main_focus_scan(char source);
//开始集中扫描
void _begin_focus_scan();
//结束集中扫描
void _end_focus_scan();
//[上行]上报集中扫描的标签数据
bool _up_station_focus_tag(char source );
//主线程中处理日常扫描的部分
void _main_daily_scan(char source);
//开始日常扫描
void _begin_daily_scan();
//结束日常扫描
void _end_daily_scan();
//[上行]上报日常扫描到的标签数据，根据模式
bool _up_station_daily_tag( char source );
//处理日常扫描到的正常的和额外增加的标签数据
bool _deal_daily_addnormal_tag(unsigned char *addtag,unsigned char *normaltag,int* pcount,int *pindex,char source );
//处理日常没有秒到的标签数据
bool _deal_daily_reduce_tag(unsigned char *normaltag,unsigned char *reducetag, int* pcount,int *pindex,char source );
//上报日常扫描正常的标签
bool _up_stationg_normaltag(  unsigned char* txbuf,int nlen,char source );
//上报日常扫描到多的标签
bool _up_station_addtag( unsigned char* txbuf,int nlen, char source );
//上报日常没有扫到的标签
bool _up_station_reducetag( unsigned char* txbuf,int nlen, char source );
//上报日常扫描的增加和减少的标签
bool _up_station_addreduce_tag(unsigned  char *addtag, unsigned  char *reducetag,int nindex,int *pindex,int *pcount,char source);
//上报日常扫描的所有标签
bool _up_station_all_tag(unsigned  char *addtag, unsigned  char *normaltag,unsigned  char *reducetag,int nindex,int *pindex,int *pcount,char source);

/*******数据处理部分******/
//为发送工作参数缓冲区填充数据
int copy_station_params_to_buf(unsigned char* txbuf,int nlen);

/*****************http部分****************/
//远程更新固件线程任务
void _ota_http_task(void *pvParameter);
//HTTP网络事件回调
esp_err_t _http_event_handler(esp_http_client_event_t *evt);


#endif
